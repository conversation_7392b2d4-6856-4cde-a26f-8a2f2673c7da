import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/broker_data_map/bloc/broker_data_map_bloc.dart';
import 'package:phoenix/features/common/broker_account_strategy_data.dart';
import 'package:phoenix/features/orders_state/bloc/orders_state_bloc.dart';
import 'package:phoenix/features/orders_state/model/unified_order_data.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/utils/app_theme.dart';

import 'package:phoenix/widgets/empty_state/empty_container.dart';
import 'package:phoenix/widgets/tile/tile_generic.dart';

class OrdersListBuilder extends StatelessWidget {
  final List<dynamic> data;
  final String emptyMessage;
  final AnimationController formSheetAnimeController;
  final bool isOrderOpen;

  const OrdersListBuilder({
    super.key,
    required this.data,
    required this.emptyMessage,
    required this.formSheetAnimeController,
    this.isOrderOpen = false,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        Future<void> pullRefresh() async {
          final authState = context.read<AuthBloc>().state; // Get current state

          if (authState is AuthAuthenticated) {
            context.read<OrdersStateBloc>().add(
                  FetchOrdersState(
                    clientId: authState.credentialsModel.clientId,
                  ),
                );
          } else {
            // Handle unauthenticated case (e.g., show login dialog)
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text("You need to log in first")),
            );
          }
        }

        if (data.isEmpty) {
          return EmptyContainer(
              title: isOrderOpen ? "No Open Orders" : "No Orders Placed",
              message: "You can place an order from the watchlist.",
              imagePath: "images/no_orders.png");
        }

        return BlocBuilder<WebSocketBloc, WebSocketState>(
          builder: (context, state) {
            Map<int, double> stockPrices = {};

            if (state is WebSocketMultipleStockPricesUpdated) {
              stockPrices = state.stockPrices;
            }

            final brokerDataMapState = context.read<BrokerDataMapBloc>().state;
            if (brokerDataMapState is! BrokerDataMapProcessedState) {
              return Center(child: Text("Something went wrong..."));
            }

            // Group orders by bucketOrderId
            final groupedOrders = _groupOrdersByBucket(data);

            return RefreshIndicator(
              color: AppTheme.primaryColor(themeState.isDarkMode),
              backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
              onRefresh: pullRefresh,
              child: ListView.builder(
                clipBehavior: Clip.hardEdge,
                padding: const EdgeInsets.all(6.0),
                itemCount: groupedOrders.length + 1,
                itemBuilder: (context, index) {
                  if (index == groupedOrders.length) {
                    // Add a SizedBox at the end
                    return const SizedBox(
                        height: 50); // Adjust height as needed
                  }

                  final orderGroup = groupedOrders[index];

                  // If it's a single order (null bucketOrderId), display normally
                  if (orderGroup.length == 1) {
                    final item = orderGroup[0];
                    final int zenId = item.positionCompKey.zenSecId;
                    final double? priceUpdate = stockPrices[zenId];

                    // Broker Account Strategy id Map
                    final brokerName = brokerDataMapState.brokerNameToLabelMap[
                            item.positionCompKey.broker] ??
                        "N/A";
                    final accountName = brokerDataMapState.accountIdToNameMap[
                            item.positionCompKey.accountId] ??
                        "N/A";
                    final strategyName = brokerDataMapState.strategyIdToNameMap[
                            item.positionCompKey.strategyId] ??
                        "N/A";
                    final brokerMetaData = BrokerAccountStrategyData(
                      brokerName: brokerName,
                      accountId: item.positionCompKey.accountId,
                      strategyId: item.positionCompKey.strategyId,
                      accountName: accountName,
                      strategyName: strategyName,
                    );

                    return TileGeneric(
                      data: item,
                      tileType: "zenOrder",
                      formAnimeController: formSheetAnimeController,
                      isOrderOpen: isOrderOpen,
                      prices: priceUpdate,
                      brokerAccountStrategyMapData: brokerMetaData,
                    );
                  } else {
                    // Display as stacked cards for bucket orders with extra spacing
                    return Container(
                      margin: const EdgeInsets.only(
                          bottom: 8.0), // Extra spacing for stacked cards
                      child: _buildStackedOrderCards(
                        orderGroup,
                        stockPrices,
                        brokerDataMapState,
                        themeState.isDarkMode,
                      ),
                    );
                  }
                },
              ),
            );
          },
        );
      },
    );
  }

  /// Groups orders by bucketOrderId
  /// Returns a list of lists where each inner list contains orders with the same bucketOrderId
  /// Orders with null bucketOrderId are kept as individual groups
  List<List<UnifiedOrderData>> _groupOrdersByBucket(List<dynamic> orders) {
    final Map<int?, List<UnifiedOrderData>> bucketGroups = {};
    final List<List<UnifiedOrderData>> result = [];

    for (final order in orders) {
      final unifiedOrder = order as UnifiedOrderData;
      final bucketId = unifiedOrder.bucketOrderId;

      if (bucketId == null) {
        // Orders with null bucketOrderId are added as individual groups
        result.add([unifiedOrder]);
      } else {
        // Group orders with the same bucketOrderId
        if (!bucketGroups.containsKey(bucketId)) {
          bucketGroups[bucketId] = [];
        }
        bucketGroups[bucketId]!.add(unifiedOrder);
      }
    }

    // Add all bucket groups to result
    result.addAll(bucketGroups.values);

    return result;
  }

  /// Builds stacked order cards for bucket orders
  Widget _buildStackedOrderCards(
    List<UnifiedOrderData> orders,
    Map<int, double> stockPrices,
    dynamic brokerDataMapState,
    bool isDarkMode,
  ) {
    return _StackedOrderCards(
      orders: orders,
      stockPrices: stockPrices,
      brokerDataMapState: brokerDataMapState,
      isDarkMode: isDarkMode,
      formSheetAnimeController: formSheetAnimeController,
      isOrderOpen: isOrderOpen,
    );
  }
}

/// Stateful widget to handle expand/collapse functionality for stacked order cards
class _StackedOrderCards extends StatefulWidget {
  final List<UnifiedOrderData> orders;
  final Map<int, double> stockPrices;
  final dynamic brokerDataMapState;
  final bool isDarkMode;
  final AnimationController? formSheetAnimeController;
  final bool isOrderOpen;

  const _StackedOrderCards({
    required this.orders,
    required this.stockPrices,
    required this.brokerDataMapState,
    required this.isDarkMode,
    required this.formSheetAnimeController,
    required this.isOrderOpen,
  });

  @override
  State<_StackedOrderCards> createState() => _StackedOrderCardsState();
}

class _StackedOrderCardsState extends State<_StackedOrderCards> {
  void _toggleExpanded() {
    _showExpandedModal();
  }

  void _showExpandedModal() {
    showDialog(
      context: context,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      builder: (context) => _BucketOrdersModal(
        orders: widget.orders,
        stockPrices: widget.stockPrices,
        brokerDataMapState: widget.brokerDataMapState,
        isDarkMode: widget.isDarkMode,
        formSheetAnimeController: widget.formSheetAnimeController,
        isOrderOpen: widget.isOrderOpen,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleExpanded,
      child: _buildStackedView(),
    );
  }

  /// Builds the stacked view when collapsed
  Widget _buildStackedView() {
    // Limit the number of visible stacked cards to 4 maximum
    final int maxVisibleCards = widget.orders.length.clamp(1, 3);
    return Container(
      margin: EdgeInsets.only(
          top: 11.0 * maxVisibleCards), // Margin for stack visibility
      child: GestureDetector(
        onTap: _toggleExpanded,
        child: SizedBox(
          height: 140, // Fixed height for consistent stacking
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              // Create background stacked cards (from back to front)
              for (int i = maxVisibleCards - 1; i > 0; i--)
                Positioned(
                  top:
                      -i * 15.0, // Smaller vertical offset for tighter stacking
                  left: i * 6.5, // Smaller horizontal offset
                  right:
                      (i * 6.5), // Negative right offset to maintain card width
                  //bottom: -i*3,

                  child: _buildOrderTile(widget.orders[i]),
                ),
              // Front card with actual content (always at position 0,0)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.15),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: _buildOrderTile(widget.orders[0]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds individual order tile
  Widget _buildOrderTile(UnifiedOrderData item) {
    final int zenId = item.positionCompKey.zenSecId;
    final double? priceUpdate = widget.stockPrices[zenId];

    // Broker Account Strategy id Map
    final brokerName = widget.brokerDataMapState
            .brokerNameToLabelMap[item.positionCompKey.broker] ??
        "N/A";
    final accountName = widget.brokerDataMapState
            .accountIdToNameMap[item.positionCompKey.accountId] ??
        "N/A";
    final strategyName = widget.brokerDataMapState
            .strategyIdToNameMap[item.positionCompKey.strategyId] ??
        "N/A";
    final brokerMetaData = BrokerAccountStrategyData(
      brokerName: brokerName,
      accountId: item.positionCompKey.accountId,
      strategyId: item.positionCompKey.strategyId,
      accountName: accountName,
      strategyName: strategyName,
    );

    return TileGeneric(
      onTap: _toggleExpanded,
      data: item,
      tileType: "zenOrder",
      formAnimeController: widget.formSheetAnimeController,
      isOrderOpen: widget.isOrderOpen,
      prices: priceUpdate,
      brokerAccountStrategyMapData: brokerMetaData,
    );
  }
}

/// Modal widget to display expanded bucket orders
class _BucketOrdersModal extends StatefulWidget {
  final List<UnifiedOrderData> orders;
  final Map<int, double> stockPrices;
  final dynamic brokerDataMapState;
  final bool isDarkMode;
  final AnimationController? formSheetAnimeController;
  final bool isOrderOpen;

  const _BucketOrdersModal({
    required this.orders,
    required this.stockPrices,
    required this.brokerDataMapState,
    required this.isDarkMode,
    required this.formSheetAnimeController,
    required this.isOrderOpen,
  });

  @override
  State<_BucketOrdersModal> createState() => _BucketOrdersModalState();
}

class _BucketOrdersModalState extends State<_BucketOrdersModal>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Slide animation for the modal
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // Fade animation for the background
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _closeModal() async {
    await _slideController.reverse();
    await _fadeController.reverse();
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
          child: SafeArea(
            child: Material(
              color: Colors.transparent,
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                child: Opacity(
                  opacity: _fadeAnimation.value,
                  child: Center(
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Container(
                    margin: const EdgeInsets.all(20),
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.8,
                      maxWidth: MediaQuery.of(context).size.width * 0.95,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Header with close button
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(20),
                              topRight: Radius.circular(20),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Bucket Orders (${widget.orders.length})',
                                style: TextStyle(
                                  color: widget.isDarkMode
                                      ? Colors.white
                                      : Colors.black,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              GestureDetector(
                                onTap: _closeModal,
                                child: Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.withValues(alpha: 0.3),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Icon(
                                    Icons.close,
                                    color: widget.isDarkMode
                                        ? Colors.white
                                        : Colors.black,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Scrollable list of orders
                        Flexible(
                          child: ListView.builder(
                            shrinkWrap: true,
                            padding: const EdgeInsets.all(6),
                            itemCount: widget.orders.length,
                            itemBuilder: (context, index) {
                              return _buildOrderTile(widget.orders[index]);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        ),
        );
      },
    );
  }

  /// Builds individual order tile for the modal
  Widget _buildOrderTile(UnifiedOrderData item) {
    final int zenId = item.positionCompKey.zenSecId;
    final double? priceUpdate = widget.stockPrices[zenId];

    // Broker Account Strategy id Map
    final brokerName = widget.brokerDataMapState
            .brokerNameToLabelMap[item.positionCompKey.broker] ??
        "N/A";
    final accountName = widget.brokerDataMapState
            .accountIdToNameMap[item.positionCompKey.accountId] ??
        "N/A";
    final strategyName = widget.brokerDataMapState
            .strategyIdToNameMap[item.positionCompKey.strategyId] ??
        "N/A";
    final brokerMetaData = BrokerAccountStrategyData(
      brokerName: brokerName,
      accountId: item.positionCompKey.accountId,
      strategyId: item.positionCompKey.strategyId,
      accountName: accountName,
      strategyName: strategyName,
    );

    return TileGeneric(
      data: item,
      tileType: "zenOrder",
      formAnimeController: widget.formSheetAnimeController,
      isOrderOpen: widget.isOrderOpen,
      prices: priceUpdate,
      brokerAccountStrategyMapData: brokerMetaData,
    );
  }
}
