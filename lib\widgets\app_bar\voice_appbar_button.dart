import 'package:flutter/material.dart';
import 'package:phoenix/screens/voice_assistant/voice_assistant_overlay.dart';

/// AppBar action button to open the Voice Assistant overlay (voice-first UX)
class VoiceCommandAppBarButton extends StatelessWidget {
  const VoiceCommandAppBarButton({super.key});

  void _openVoiceOverlay(BuildContext context) {
    Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        barrierColor: Colors.black.withOpacity(0.85),
        pageBuilder: (_, __, ___) => const VoiceAssistantOverlay(),
        transitionsBuilder: (_, anim, __, child) {
          return FadeTransition(opacity: anim, child: child);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      tooltip: 'Voice Assistant',
      icon: const Icon(Icons.mic),
      onPressed: () => _openVoiceOverlay(context),
    );
  }
}