import 'dart:math' as math;
import 'package:flutter/material.dart';

/// Widget to display current voice input state and partial text
class VoiceInputWidget extends StatefulWidget {
  final String? partialText;
  final bool isListening;
  final bool isPaused;
  final bool isFinalizing;
  final bool isProcessing;
  final String? statusText;
  final VoidCallback? onStopListening;

  const VoiceInputWidget({
    super.key,
    this.partialText,
    this.isListening = false,
    this.isPaused = false,
    this.isFinalizing = false,
    this.isProcessing = false,
    this.statusText,
    this.onStopListening,
  });

  @override
  State<VoiceInputWidget> createState() => _VoiceInputWidgetState();
}

class _VoiceInputWidgetState extends State<VoiceInputWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _waveController;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );
    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    if (widget.isListening || widget.isPaused || widget.isFinalizing) {
      _startAnimations();
    }
  }

  @override
  void didUpdateWidget(VoiceInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    final wasActive = oldWidget.isListening || oldWidget.isPaused || oldWidget.isFinalizing;
    final isActive = widget.isListening || widget.isPaused || widget.isFinalizing;
    
    if (wasActive != isActive) {
      if (isActive) {
        _startAnimations();
      } else {
        _stopAnimations();
      }
    }
  }

  void _startAnimations() {
    _pulseController.repeat();
    _waveController.repeat();
  }

  void _stopAnimations() {
    _pulseController.stop();
    _waveController.stop();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isActive = widget.isListening || widget.isPaused || widget.isFinalizing || widget.isProcessing;
    if (!isActive && widget.partialText == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: _getGradientColors(),
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getBorderColor(),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Voice visualization
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildVoiceIcon(),
              const SizedBox(width: 16),
              if (widget.isListening || widget.isPaused) _buildWaveVisualization(),
              if (widget.isFinalizing) _buildFinalizingIndicator(),
              if (widget.isProcessing) _buildProcessingIndicator(),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Status text
          Text(
            widget.statusText ?? _getDefaultStatusText(),
            style: TextStyle(
              color: Colors.grey.shade200,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Partial text
          if (widget.partialText != null && widget.partialText!.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(top: 12),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.8),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey.shade300,
                  width: 1,
                ),
              ),
              child: Text(
                '"${widget.partialText}"',
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          
          // Stop button for active states
          if ((widget.isListening || widget.isPaused) && widget.onStopListening != null)
            Container(
              margin: const EdgeInsets.only(top: 12),
              child: TextButton.icon(
                onPressed: widget.onStopListening,
                icon: const Icon(Icons.stop, size: 18),
                label: const Text('Stop'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red.shade700,
                  backgroundColor: Colors.red.shade50,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildVoiceIcon() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        final scale = (widget.isListening || widget.isPaused || widget.isFinalizing)
            ? 1.0 + (math.sin(_pulseController.value * math.pi * 2) * 0.1)
            : 1.0;
        
        return Transform.scale(
          scale: scale,
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: _getIconGradientColors(),
              ),
              boxShadow: [
                BoxShadow(
                  color: _getIconShadowColor().withOpacity(0.3),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Icon(
              _getIconForState(),
              color: Colors.white,
              size: 24,
            ),
          ),
        );
      },
    );
  }

  Widget _buildWaveVisualization() {
    return SizedBox(
      width: 120,
      height: 32,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(5, (index) {
          return AnimatedBuilder(
            animation: _waveController,
            builder: (context, child) {
              final delay = index * 0.2;
              final phase = (_waveController.value + delay) % 1.0;
              final intensity = (math.sin(phase * math.pi * 2) + 1) / 2;
              final height = 4 + (intensity * 24);
              
              return Container(
                width: 3,
                height: height,
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.blue.shade600,
                      Colors.purple.shade600,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(2),
                ),
              );
            },
          );
        }),
      ),
    );
  }

  Widget _buildProcessingIndicator() {
    return SizedBox(
      width: 120,
      height: 32,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: Colors.orange.shade600,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Processing...',
            style: TextStyle(
              color: Colors.orange.shade700,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _getDefaultStatusText() {
    if (widget.isListening) {
      return 'Listening for your command...';
    } else if (widget.isPaused) {
      return 'Paused - continue speaking or wait...';
    } else if (widget.isFinalizing) {
      return 'Finalizing your input...';
    } else if (widget.isProcessing) {
      return 'Processing your request...';
    } else {
      return 'Voice input ready';
    }
  }

  /// Get gradient colors for the main container
  List<Color> _getGradientColors() {
    if (widget.isListening) {
      return [
        Colors.blue.withOpacity(0.1),
        Colors.purple.withOpacity(0.1),
      ];
    } else if (widget.isPaused) {
      return [
        Colors.amber.withOpacity(0.1),
        Colors.orange.withOpacity(0.1),
      ];
    } else if (widget.isFinalizing) {
      return [
        Colors.green.withOpacity(0.1),
        Colors.teal.withOpacity(0.1),
      ];
    } else {
      return [
        Colors.orange.withOpacity(0.1),
        Colors.red.withOpacity(0.1),
      ];
    }
  }

  /// Get border color for the main container
  Color _getBorderColor() {
    if (widget.isListening) {
      return Colors.blue.withOpacity(0.3);
    } else if (widget.isPaused) {
      return Colors.amber.withOpacity(0.3);
    } else if (widget.isFinalizing) {
      return Colors.green.withOpacity(0.3);
    } else {
      return Colors.orange.withOpacity(0.3);
    }
  }

  /// Get gradient colors for the voice icon
  List<Color> _getIconGradientColors() {
    if (widget.isListening) {
      return [Colors.blue.shade400, Colors.purple.shade400];
    } else if (widget.isPaused) {
      return [Colors.amber.shade400, Colors.orange.shade400];
    } else if (widget.isFinalizing) {
      return [Colors.green.shade400, Colors.teal.shade400];
    } else if (widget.isProcessing) {
      return [Colors.orange.shade400, Colors.red.shade400];
    } else {
      return [Colors.grey.shade400, Colors.grey.shade500];
    }
  }

  /// Get shadow color for the voice icon
  Color _getIconShadowColor() {
    if (widget.isListening) {
      return Colors.blue;
    } else if (widget.isPaused) {
      return Colors.amber;
    } else if (widget.isFinalizing) {
      return Colors.green;
    } else {
      return Colors.orange;
    }
  }

  /// Get icon for current state
  IconData _getIconForState() {
    if (widget.isListening) {
      return Icons.mic;
    } else if (widget.isPaused) {
      return Icons.pause;
    } else if (widget.isFinalizing) {
      return Icons.check;
    } else if (widget.isProcessing) {
      return Icons.psychology;
    } else {
      return Icons.mic_off;
    }
  }

  /// Get wave colors based on state
  List<Color> _getWaveColors() {
    if (widget.isPaused) {
      return [Colors.amber.shade600, Colors.orange.shade600];
    } else {
      return [Colors.blue.shade600, Colors.purple.shade600];
    }
  }

  /// Build finalizing indicator
  Widget _buildFinalizingIndicator() {
    return SizedBox(
      width: 120,
      height: 32,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: Colors.green.shade600,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Finalizing...',
            style: TextStyle(
              color: Colors.green.shade700,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
