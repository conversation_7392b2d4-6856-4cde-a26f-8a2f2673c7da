import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:phoenix/features/fetch_clients_data/data/repo/clients_repo.dart';
import 'package:phoenix/features/fetch_clients_data/model/client_data.dart';

part 'clients_data_event.dart';
part 'clients_data_state.dart';

class ClientsDataBloc extends Bloc<ClientsDataEvent, ClientsDataState> {

  final ClientsDataRepo _clientsDataRepo;

  ClientsDataBloc(this._clientsDataRepo) : super(ClientsDataInitial()) {
    on<FetchClientsData>(_onFetchClientData);
  }

  void _onFetchClientData(ClientsDataEvent event, Emitter<ClientsDataState> emit) async {
    debugPrint("reach hi _onFetchClientData");
    emit(ClientsDataLoading());
    try{
      final clientsList = await _clientsDataRepo.getClients();
      debugPrint(clientsList.toString());
      emit(ClientsDataLoaded(clientsList: clientsList));
      debugPrint("Emitted ClientsDataLoaded");
    }catch (e) {
      emit(ClientsDataError(message: e.toString()));
    }
  }


}
