import 'package:flutter/material.dart';
import 'package:phoenix/services/voice_testing_service.dart';
import 'package:phoenix/services/voice_assistant_service.dart';
import 'package:phoenix/models/voice_command_model.dart';

/// Widget for testing voice commands during development
class VoiceTestingWidget extends StatefulWidget {
  const VoiceTestingWidget({Key? key}) : super(key: key);

  @override
  State<VoiceTestingWidget> createState() => _VoiceTestingWidgetState();
}

class _VoiceTestingWidgetState extends State<VoiceTestingWidget> {
  final VoiceTestingService _testingService = VoiceTestingService();
  final VoiceAssistantService _voiceAssistant = VoiceAssistantService();
  final TextEditingController _commandController = TextEditingController();
  
  VoiceCommand? _lastCommand;
  String _lastResponse = '';
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _setupVoiceCommandListener();
  }

  void _setupVoiceCommandListener() {
    _voiceAssistant.commandStream.listen((command) {
      setState(() {
        _lastCommand = command;
      });
    });
  }

  Future<void> _testCommand(String command) async {
    setState(() {
      _isProcessing = true;
      _lastResponse = '';
    });

    try {
      await _testingService.testVoiceCommand(command);
      setState(() {
        _lastResponse = 'Command processed successfully';
      });
    } catch (e) {
      setState(() {
        _lastResponse = 'Error: $e';
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.mic, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Voice Command Testing',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Voice-first UX: remove manual text field and quick test chips
            Center(
              child: Text(
                'Use the mic button in the app bar or FAB to issue voice commands.',
                style: TextStyle(color: Colors.grey[700]),
              ),
            ),
            
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            
            // Last command result
            if (_lastCommand != null) ...[
              const Text(
                'Last Processed Command:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Action: ${_lastCommand!.action.name}'),
                    if (_lastCommand!.symbol != null)
                      Text('Symbol: ${_lastCommand!.symbol}'),
                    if (_lastCommand!.quantity != null)
                      Text('Quantity: ${_lastCommand!.quantity}'),
                    if (_lastCommand!.orderType != null)
                      Text('Order Type: ${_lastCommand!.orderType!.name}'),
                    if (_lastCommand!.originalText != null)
                      Text('Original: "${_lastCommand!.originalText}"'),
                    Text('Complete: ${_lastCommand!.isComplete}'),
                    if (!_lastCommand!.isComplete)
                      Text('Missing: ${_lastCommand!.missingParameters.join(', ')}'),
                  ],
                ),
              ),
            ],
            
            if (_lastResponse.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Response: $_lastResponse',
                style: TextStyle(
                  color: _lastResponse.startsWith('Error') ? Colors.red : Colors.green,
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Instructions
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue[700], size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'How to Test:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    '1. Use the quick test buttons above\n'
                    '2. Type custom commands in the text field\n'
                    '3. Watch the "Last Processed Command" section for results\n'
                    '4. Listen for text-to-speech responses',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _commandController.dispose();
    super.dispose();
  }
}