import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/authentication/model/credentials_model.dart';
import 'package:phoenix/features/broker_account_strategy_selection/bloc/broker_account_strategy_selection_bloc.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/services/shared_prefrences_service.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/order_form/order_form_margin_viewer.dart';
import 'package:phoenix/widgets/order_form/selection_drop_down.dart';

class BrokerAccountStrategyModal extends StatefulWidget {
  final bool showSetAsDefault;
  final ValueChanged<int> onDefaultSet;
  const BrokerAccountStrategyModal(
      {super.key, this.showSetAsDefault = true, required this.onDefaultSet});
  @override
  State<BrokerAccountStrategyModal> createState() =>
      _BrokerAccountStrategyModalState();
}

class _BrokerAccountStrategyModalState
    extends State<BrokerAccountStrategyModal> {
  bool _setAsDefault = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final prefs = SharedPreferencesService.instance;
      final authState = context.read<AuthBloc>().state;
      int? clientId;
      if (authState is AuthAuthenticated) {
        clientId = authState.credentialsModel.clientId;
      }
      if (clientId == null) return;
      final defaultBroker = prefs.getDefaultBroker(clientId);
      final defaultAccount = prefs.getDefaultAccount(clientId);
      final defaultStrategy = prefs.getDefaultStrategy(clientId);

      // Get the default names as well
      final defaultBrokerName =
          defaultBroker?.toLowerCase() == "all" ? "All" : defaultBroker;
      final defaultAccountName = prefs.getDefaultAccountName(clientId);
      final defaultStrategyName = prefs.getDefaultStrategyName(clientId);

      setState(() {
        _setAsDefault = (defaultBroker != null &&
            defaultAccount != null &&
            defaultStrategy != null);
      });
      final bloc = context.read<BrokerAccountStrategySelectionBloc>();
      final state = bloc.state;
      // Find and select default broker
      if (defaultBroker != null) {
        // Check if the default broker is "all"
        if (defaultBroker.toLowerCase() == "all") {
          // Create a special "All" broker with a unique ID
          if (state.brokers.length > 1) {
            final allBroker = BrokerInfo(
              brokerId: -1, // Use a special ID for "All"
              brokerName: "All",
              accounts: [], // Empty accounts list
            );
            bloc.add(BrokerSelected(allBroker));
            await Future.delayed(
                Duration(milliseconds: 100)); // Wait for state update
          }
        } else {
          // Find the specific broker
          final broker = state.brokers.firstWhere(
            (b) => b.brokerName == defaultBroker,
            orElse: () => state.brokers.first,
          );
          if (broker != state.selectedBroker) {
            bloc.add(BrokerSelected(broker));
            await Future.delayed(
                Duration(milliseconds: 100)); // Wait for state update
          }
        }

        // Find and select default account
        if (defaultAccount != null) {
          // Check if the default account is "all"
          if (defaultAccount.toLowerCase() == "all") {
            // Create a special "All" account with a unique ID
            if (bloc.state.availableAccounts.length > 1) {
              final allAccount = AccountInfo(
                accountId: -1, // Use a special ID for "All"
                accountName: "All",
                strategies: [], // Empty strategies list
              );
              bloc.add(AccountSelected(allAccount));
              await Future.delayed(Duration(milliseconds: 100));
            }
          } else {
            // Find the specific account
            final account = bloc.state.availableAccounts.firstWhere(
              (a) => a.accountId.toString() == defaultAccount,
              orElse: () => bloc.state.availableAccounts.first,
            );
            if (account != bloc.state.selectedAccount) {
              bloc.add(AccountSelected(account));
              await Future.delayed(Duration(milliseconds: 100));
            }
          }

          // Find and select default strategy
          if (defaultStrategy != null) {
            // Check if the default strategy is "all"
            if (defaultStrategy.toLowerCase() == "all") {
              // Create a special "All" strategy with a unique ID
              if (bloc.state.availableStrategies.length > 1) {
                final allStrategy = Strategy(
                  strategyId: -1, // Use a special ID for "All"
                  strategyName: "All",
                );
                bloc.add(StrategySelected(allStrategy));
              }
            } else {
              // Find the specific strategy
              final strategy = bloc.state.availableStrategies.firstWhere(
                (s) => s.strategyId.toString() == defaultStrategy,
                orElse: () => bloc.state.availableStrategies.first,
              );
              if (strategy != bloc.state.selectedStrategy) {
                bloc.add(StrategySelected(strategy));
              }
            }
          }
        }
      }
    });
  }

  void onCancel(BuildContext context) {
    Navigator.of(context).pop();
  }

  void onConfirm(BuildContext context) {
    final state = context.read<BrokerAccountStrategySelectionBloc>().state;
    final brokerName = state.selectedBroker?.brokerName;
    final accountId = state.selectedAccount?.accountId;
    final strategyId = state.selectedStrategy?.strategyId;
    final accountName = state.selectedAccount?.accountName;
    final strategyName = state.selectedStrategy?.strategyName;
    final prefs = SharedPreferencesService.instance;
    final authState = context.read<AuthBloc>().state;
    int? clientId;
    if (authState is AuthAuthenticated) {
      clientId = authState.credentialsModel.clientId;
    }

    // Check if any of the selections is "All" (has ID of -1)
    final isAllBroker =
        brokerName == "All" || (state.selectedBroker?.brokerId == -1);
    final isAllAccount = accountName == "All" || (accountId == -1);
    final isAllStrategy = strategyName == "All" || (strategyId == -1);

    // Save the selections to preferences
    if (_setAsDefault && clientId != null) {
      // For broker
      final brokerToSave = isAllBroker ? "all" : brokerName;

      // For account
      final accountIdToSave =
          isAllAccount ? "all" : (accountId?.toString() ?? "");
      final accountNameToSave = isAllAccount ? "All" : accountName;

      // For strategy
      final strategyIdToSave =
          isAllStrategy ? "all" : (strategyId?.toString() ?? "");
      final strategyNameToSave = isAllStrategy ? "All" : strategyName;

      if (brokerToSave != null) {
        prefs.saveDefaultSelections(
          clientId: clientId,
          brokerName: brokerToSave,
          accountId: accountIdToSave,
          strategyId: strategyIdToSave,
          accountName: accountNameToSave,
          strategyName: strategyNameToSave,
        );
        widget.onDefaultSet(clientId);
      }
    } else if (!_setAsDefault && clientId != null) {
      prefs.clearDefaultSelections(clientId);
    }

    // Prepare the result with the actual selected values (including "All" options)
    final result = {
      'brokerName': isAllBroker ? "all" : brokerName,
      'accountId': isAllAccount ? "all" : accountId,
      'strategyId': isAllStrategy ? "all" : strategyId,
      'accountName': isAllAccount ? "All" : accountName,
      'strategyName': isAllStrategy ? "All" : strategyName,
      'setAsDefault': _setAsDefault,
    };
    Navigator.of(context).pop(result);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Dialog(
            backgroundColor: AppTheme.surfaceColor(themeState.isDarkMode),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
              constraints: const BoxConstraints(maxWidth: 400),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with title and close button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Select Account',
                        style: TextStyle(
                          color: AppTheme.textPrimary(themeState.isDarkMode),
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: Icon(Icons.close,
                            color:
                                AppTheme.textSecondary(themeState.isDarkMode)),
                        onPressed: () => Navigator.of(context).pop(),
                        splashRadius: 20,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Broker Dropdown
                  _buildDropdownField(
                    context: context,
                    label: "Broker",
                    themeState: themeState,
                    child: BlocBuilder<BrokerAccountStrategySelectionBloc,
                        BrokerAccountStrategyState>(
                      builder: (context, state) {
                        // Create a list of brokers with "All" option if there's more than one broker
                        List<BrokerInfo> brokerItems = [...state.brokers];
                        if (brokerItems.length > 1) {
                          // Create a special "All" broker with a unique ID
                          final allBroker = BrokerInfo(
                            brokerId: -1, // Use a special ID for "All"
                            brokerName: "All",
                            accounts: [], // Empty accounts list
                          );
                          brokerItems.insert(0, allBroker);
                        }

                        // Check if the current selection is "All" but not in the items list
                        BrokerInfo? selectedBroker = state.selectedBroker;

                        // If we have an "All" broker in the list, make sure it's used for selection
                        final allBrokerInList = brokerItems.firstWhere(
                            (b) => b.brokerName == "All" || b.brokerId == -1,
                            orElse: () => BrokerInfo(
                                brokerId: -1, brokerName: "All", accounts: []));

                        if (selectedBroker?.brokerName == "All" ||
                            selectedBroker?.brokerId == -1) {
                          // If the selected broker is "All", use the one from the list
                          if (brokerItems.any((b) =>
                              b.brokerName == "All" || b.brokerId == -1)) {
                            selectedBroker = allBrokerInList;
                          } else {
                            // Add the "All" broker to the list if it's selected but not in the list
                            brokerItems.insert(0, allBrokerInList);
                            selectedBroker = allBrokerInList;
                          }
                        }

                        return SelectionDropdown<BrokerInfo>(
                          hint: "Select broker",
                          selectedValue: selectedBroker,
                          items: brokerItems,
                          getItemLabel: (broker) => broker.brokerName,
                          onChanged: (broker) {
                            if (broker != null) {
                              context
                                  .read<BrokerAccountStrategySelectionBloc>()
                                  .add(BrokerSelected(broker));
                            }
                          },
                          dropdownDecoration: BoxDecoration(
                            color: ThemeConstants.getDropdownBackgroundColor(
                                themeState.isDarkMode),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                color: ThemeConstants.getDropdownBorderColor(
                                    themeState.isDarkMode)),
                          ),
                          itemTextStyle:
                              ThemeConstants.getDropdownItemTextStyle(
                                  themeState.isDarkMode),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Account Dropdown
                  _buildDropdownField(
                    context: context,
                    label: "Account",
                    themeState: themeState,
                    child: BlocBuilder<BrokerAccountStrategySelectionBloc,
                        BrokerAccountStrategyState>(
                      builder: (context, state) {
                        // Create a list of accounts with "All" option if there's more than one account
                        List<AccountInfo> accountItems = [
                          ...state.availableAccounts
                        ];
                        if (accountItems.length > 1) {
                          // Create a special "All" account with a unique ID
                          final allAccount = AccountInfo(
                            accountId: -1, // Use a special ID for "All"
                            accountName: "All",
                            strategies: [], // Empty strategies list
                          );
                          accountItems.insert(0, allAccount);
                        }

                        // Check if the current selection is "All" but not in the items list
                        AccountInfo? selectedAccount = state.selectedAccount;

                        // If we have an "All" account in the list, make sure it's used for selection
                        final allAccountInList = accountItems.firstWhere(
                            (a) => a.accountName == "All" || a.accountId == -1,
                            orElse: () => AccountInfo(
                                accountId: -1,
                                accountName: "All",
                                strategies: []));

                        if (selectedAccount?.accountName == "All" ||
                            selectedAccount?.accountId == -1) {
                          // If the selected account is "All", use the one from the list
                          if (accountItems.any((a) =>
                              a.accountName == "All" || a.accountId == -1)) {
                            selectedAccount = allAccountInList;
                          } else {
                            // Add the "All" account to the list if it's selected but not in the list
                            accountItems.insert(0, allAccountInList);
                            selectedAccount = allAccountInList;
                          }
                        } else if (!state.availableAccounts
                            .contains(state.selectedAccount)) {
                          selectedAccount = null;
                        }

                        return SelectionDropdown<AccountInfo>(
                          hint: "Select account",
                          selectedValue: selectedAccount,
                          items: accountItems,
                          getItemLabel: (account) => account.accountName,
                          onChanged: (account) {
                            if (account != null) {
                              context
                                  .read<BrokerAccountStrategySelectionBloc>()
                                  .add(AccountSelected(account));
                            }
                          },
                          dropdownDecoration: BoxDecoration(
                            color: ThemeConstants.getDropdownBackgroundColor(
                                themeState.isDarkMode),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                color: ThemeConstants.getDropdownBorderColor(
                                    themeState.isDarkMode)),
                          ),
                          itemTextStyle:
                              ThemeConstants.getDropdownItemTextStyle(
                                  themeState.isDarkMode),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Strategy Dropdown
                  _buildDropdownField(
                    context: context,
                    label: "Strategy",
                    themeState: themeState,
                    child: BlocBuilder<BrokerAccountStrategySelectionBloc,
                        BrokerAccountStrategyState>(
                      builder: (context, state) {
                        // Create a list of strategies with "All" option if there's more than one strategy
                        List<Strategy> strategyItems = [
                          ...state.availableStrategies
                        ];
                        if (strategyItems.length > 1) {
                          // Create a special "All" strategy with a unique ID
                          final allStrategy = Strategy(
                            strategyId: -1, // Use a special ID for "All"
                            strategyName: "All",
                          );
                          strategyItems.insert(0, allStrategy);
                        }

                        // Check if the current selection is "All" but not in the items list
                        Strategy? selectedStrategy = state.selectedStrategy;

                        // If we have an "All" strategy in the list, make sure it's used for selection
                        final allStrategyInList = strategyItems.firstWhere(
                            (s) =>
                                s.strategyName == "All" || s.strategyId == -1,
                            orElse: () =>
                                Strategy(strategyId: -1, strategyName: "All"));

                        if (selectedStrategy?.strategyName == "All" ||
                            selectedStrategy?.strategyId == -1) {
                          // If the selected strategy is "All", use the one from the list
                          if (strategyItems.any((s) =>
                              s.strategyName == "All" || s.strategyId == -1)) {
                            selectedStrategy = allStrategyInList;
                          } else {
                            // Add the "All" strategy to the list if it's selected but not in the list
                            strategyItems.insert(0, allStrategyInList);
                            selectedStrategy = allStrategyInList;
                          }
                        } else if (!state.availableStrategies
                            .contains(state.selectedStrategy)) {
                          selectedStrategy = null;
                        }

                        return SelectionDropdown<Strategy>(
                          hint: "Select strategy",
                          selectedValue: selectedStrategy,
                          items: strategyItems,
                          getItemLabel: (strategy) => strategy.strategyName,
                          onChanged: (strategy) {
                            if (strategy != null) {
                              context
                                  .read<BrokerAccountStrategySelectionBloc>()
                                  .add(StrategySelected(strategy));
                            }
                          },
                          dropdownDecoration: BoxDecoration(
                            color: ThemeConstants.getDropdownBackgroundColor(
                                themeState.isDarkMode),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                color: ThemeConstants.getDropdownBorderColor(
                                    themeState.isDarkMode)),
                          ),
                          itemTextStyle:
                              ThemeConstants.getDropdownItemTextStyle(
                                  themeState.isDarkMode),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 20),
                  // Balance Row
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        "Balance",
                        style: TextStyle(
                          color: AppTheme.textSecondary(themeState.isDarkMode),
                          fontSize: 15,
                        ),
                      ),
                      const SizedBox(width: 10), // Add spacing if needed
                      Expanded(
                        child: OrferFormMarginViewer(),
                      ),
                    ],
                  ),
                  if (widget.showSetAsDefault) ...[
                    const SizedBox(height: 18),
                    Row(
                      children: [
                        SizedBox(
                          width: 24,
                          height: 24,
                          child: Checkbox(
                            value: _setAsDefault,
                            onChanged: (value) {
                              setState(() {
                                _setAsDefault = value ?? false;
                              });
                            },
                            fillColor: WidgetStateProperty.resolveWith<Color>(
                              (Set<WidgetState> states) {
                                if (states.contains(WidgetState.selected)) {
                                  return AppTheme.primaryColor(
                                      themeState.isDarkMode);
                                }
                                return AppTheme.borderColor(
                                    themeState.isDarkMode);
                              },
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Text(
                            "Set as default for future orders",
                            style: TextStyle(
                              color:
                                  AppTheme.textPrimary(themeState.isDarkMode),
                              fontSize: 15,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(
                                color: AppTheme.borderColor(
                                    themeState.isDarkMode)),
                            foregroundColor:
                                AppTheme.textSecondary(themeState.isDarkMode),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text("Cancel",
                              style: TextStyle(fontWeight: FontWeight.w600)),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => onConfirm(context),
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                AppTheme.primaryColor(themeState.isDarkMode),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            "Continue",
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ));
      },
    );
  }

  Widget _buildDropdownField({
    required BuildContext context,
    required String label,
    required Widget child,
    required ThemeState themeState,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Text(
            label,
            style: TextStyle(
              color: AppTheme.textSecondary(themeState.isDarkMode),
              fontSize: 14,
            ),
          ),
        ),
        child,
      ],
    );
  }
}
